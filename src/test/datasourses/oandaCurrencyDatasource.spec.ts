import { expect } from "chai";
import { stub } from "sinon";
import config from "../../skywind/config";
import { OANDACurrencyDatasource } from "../../skywind/datasources/OANDACurrencyDatasource";
import { Provider } from "../../skywind/providers/provider";
import { logging } from "@skywind-group/sw-utils";
import * as superagent from "superagent";

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

describe("OANDA Currency Datasource", () => {
    const originalConfig = config.oanda;
    let superagentStub: sinon.SinonStub;

    const mockResponseData1 = {
        "quotes": [
            {
                "base_currency": "USD",
                "quote_currency": "EUR",
                "average_bid": "0.861603",
                "average_ask": "0.861704"
            },
            {
                "base_currency": "USD",
                "quote_currency": "CNY",
                "average_bid": "6.83090",
                "average_ask": "6.83216"
            },
            {
                "base_currency": "USD",
                "quote_currency": "VND",
                "average_bid": "23227.4",
                "average_ask": "23363.2"
            },
            {
                "base_currency": "EUR",
                "quote_currency": "USD",
                "average_bid": "1.16049",
                "average_ask": "1.16063"
            },
            {
                "base_currency": "EUR",
                "quote_currency": "CNY",
                "average_bid": "7.92719",
                "average_ask": "7.92960"
            },
        ]
    };

    before(() => {
        config.oanda.apiKey = "test";
        superagentStub = stub(superagent, "get");
    });

    after(() => {
        config.oanda = originalConfig;
        superagentStub.restore();
    });

    it("get rates", async () => {
        const mockRequest = {
            query: stub().returnsThis(),
            then: stub(),
            catch: stub()
        };

        superagentStub.returns(mockRequest);
        mockRequest.query.returns({
            body: mockResponseData1
        });

        const provider = new OANDACurrencyDatasource();
        const today = new Date();
        const rates = await provider.load(today, ["USD", "EUR"]);
        expect(rates).to.deep.equal({
            "provider": Provider.OANDA,
            "bidRates": {
                "CNY": {
                    "EUR": 0.12611,
                    "USD": 0.14637
                },
                "EUR": {
                    "CNY": 7.92719,
                    "USD": 1.16049
                },
                "USD": {
                    "CNY": 6.8309,
                    "EUR": 0.861603,
                    "VND": 23227.4
                },
                "VND": {
                    "USD": 0.00004
                }
            },
            "askRates": {
                "CNY": {
                    "EUR": 0.12615,
                    "USD": 0.14639
                },
                "EUR": {
                    "CNY": 7.9296,
                    "USD": 1.16063
                },
                "USD": {
                    "CNY": 6.83216,
                    "EUR": 0.861704,
                    "VND": 23363.2
                },
                "VND": {
                    "USD": 0.00004
                }
            },
            "ts": today
        });
    });

    it("ignore rates with zero", async () => {
        const mockResponseData2 = {
            "quotes": [
                {
                    "base_currency": "USD",
                    "quote_currency": "EUR",
                    "average_bid": "0.861603",
                    "average_ask": "0.861704"
                },
                {
                    "base_currency": "EUR",
                    "quote_currency": "USD",
                    "average_bid": "1.16049",
                    "average_ask": "1.16063"
                },
                {
                    "base_currency": "EUR",
                    "quote_currency": "BTC",
                    "average_bid": "0",
                    "average_ask": "0"
                },
            ]
        };

        const mockRequest = {
            query: stub().returnsThis(),
            then: stub(),
            catch: stub()
        };

        superagentStub.returns(mockRequest);
        mockRequest.query.returns({
            body: mockResponseData2
        });

        const provider = new OANDACurrencyDatasource();
        const today = new Date();
        const rates = await provider.load(today, ["USD", "EUR"]);
        expect(rates).to.deep.equal({
            "provider": Provider.OANDA,
            "bidRates": {
                "BTC": {},
                "EUR": {
                    "USD": 1.16049
                },
                "USD": {
                    "EUR": 0.861603,
                }
            },
            "askRates": {
                "BTC": {},
                "EUR": {
                    "USD": 1.16063
                },
                "USD": {
                    "EUR": 0.861704,
                }
            },
            "ts": today
        });
    });
});
