import * as superagent from "superagent";
import config from "../config";
import logger from "../logger";
import type { CurrencyDatasource, ProviderRates } from "../providers/provider";
import { Provider } from "../providers/provider";
import { getTimestamp } from "../providers/utils";

const log = logger();

export class OXRCurrencyDatasource implements CurrencyDatasource {

    public async load(date: Date, baseCurrencies: string[]): Promise<ProviderRates> {
        const requests = baseCurrencies.map((baseCurrency) => this.requestRates(date, baseCurrency));
        const responses = await Promise.all(requests);
        const result: ProviderRates = {
            provider: Provider.OXR,
            ts: date,
            bidRates: {},
            askRates: {}
        };
        for (const data of responses) {
            result.bidRates[data["base"]] = result.askRates[data["base"]] = data["rates"];
        }
        return result;
    }

    private async requestRates(date: Date, baseCurrency: string): Promise<any> {
        const url = `https://openexchangerates.org/api/historical/${getTimestamp(date)}.json`;
        const query = {
            base: baseCurrency,
            show_alternative: true
        };

        log.info({
            method: "GET",
            url,
            query: query
        }, "GET %s", url);

        try {
            const res = await superagent
                .get(url)
                .query(query)
                .set("Authorization", `Token ${config.oxr.appKey}`);
            return res.body;
        } catch (error: any) {
            if (error.response) {
                const text = error.response.text || JSON.stringify(error.response.body);
                throw new Error(`OXR internal error: ${text}, status=${error.response.status}`);
            }
            throw error;
        }
    }
}
